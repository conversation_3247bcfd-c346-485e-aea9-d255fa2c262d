import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '客户代号',
    align:"center",
    dataIndex: 'customerCode'
   },
   {
    title: '客户名称',
    align:"center",
    dataIndex: 'customerName'
   },
   {
    title: '省',
    align:"center",
    dataIndex: 'province'
   },
   {
    title: '市',
    align:"center",
    dataIndex: 'city'
   },
   {
    title: '区',
    align:"center",
    dataIndex: 'area'
   },
   {
    title: '地址',
    align:"center",
    dataIndex: 'address'
   },
   {
    title: '客户级别',
    align:"center",
    dataIndex: 'level'
   },
   {
    title: '客户行业',
    align:"center",
    dataIndex: 'industry'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '客户代号',
    field: 'customerCode',
    component: 'Input',
  },
  {
    label: '客户名称',
    field: 'customerName',
    component: 'Input',
  },
  {
    label: '省',
    field: 'province',
    component: 'Input',
  },
  {
    label: '市',
    field: 'city',
    component: 'Input',
  },
  {
    label: '区',
    field: 'area',
    component: 'Input',
  },
  {
    label: '地址',
    field: 'address',
    component: 'Input',
  },
  {
    label: '客户级别',
    field: 'level',
    component: 'Input',
  },
  {
    label: '客户行业',
    field: 'industry',
    component: 'Input',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  customerCode: {title: '客户代号',order: 0,view: 'text', type: 'string',},
  customerName: {title: '客户名称',order: 1,view: 'text', type: 'string',},
  province: {title: '省',order: 2,view: 'text', type: 'string',},
  city: {title: '市',order: 3,view: 'text', type: 'string',},
  area: {title: '区',order: 4,view: 'text', type: 'string',},
  address: {title: '地址',order: 5,view: 'text', type: 'string',},
  level: {title: '客户级别',order: 6,view: 'text', type: 'string',},
  industry: {title: '客户行业',order: 7,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}